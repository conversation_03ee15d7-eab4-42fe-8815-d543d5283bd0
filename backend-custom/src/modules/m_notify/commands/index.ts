import { CreateNotify<PERSON><PERSON><PERSON>Handler } from './create-notify.command';
import { UpdateNotifyCommandHandler } from './update-notify.command';
import { DeleteNotifyCommandHandler } from './delete-notify.command';
import { MarkReadNotifyCommandHandler } from './mark-read-notify.command';
import { MarkAllReadNotifyHandler } from './mark-all-read-notify.handler';

export const CommandHandlers = [
  CreateNotifyCommandHandler,
  UpdateNotifyCommandHandler,
  DeleteNotify<PERSON>ommandHandler,
  <PERSON><PERSON><PERSON><PERSON>otify<PERSON>ommand<PERSON>andler,
  MarkAllReadNotifyHandler,
];
