#REGION APPLICATION_CONFIG - Những cấu hình mạc định của bất kỳ ứng dụng nào:
## TIME_ZONE: Quy định múi giờ hiện tại của server
## TZ: Quy định múi giờ hiện tại của server, giong cai (Do dung module th3)
TIME_ZONE=Asia/Ho_Chi_Minh
TZ=${TIME_ZONE}

## SERVER_RUINING_AT_PORT: Khởi chạy server ở port
SERVER_RUNING_AT_PORT=3000

## API Version
API_VERSION_HEADER=2.0.0
## API Name: quy định tên api phục vụ cho việc thống nhất khi sử dụng prefix trong các module dùng chung
## Lưu ý góp vào thành phần của url api 
API_NAME_PREFIX=cak-v1

## ENABLE_LOGGER: Server sẽ logs ra console nếu biến này được bậc
ENABLE_LOGGER=true

## ENABLE_CENTRALIZED_LOGGER: Mỗi khi có error từ server - logs sẽ được gửi qua service logs tập trung
ENABLE_CENTRALIZED_LOGGER=true

## ENABLE_CENTRALIZED_AUTHENTICATE: Su dung phan quyen tap trung
ENABLE_CENTRALIZED_AUTHENTICATION=false

## Swagger document sẽ được chạy 
## tại `http://host:port/_docs` nếu ENABLE_SWAGGER_DOCS được bậc
ENABLE_SWAGGER_DOCS=true
SWAGGER_DOCUMENT_PATH=/_docs

## Assets path in assets folder
## Value: assets/uef, assets/hutech, assets/royal
ASSETS_PATH=assets

# LOG perf
LOG_BUFFER_TIME=10000

# Config module system file v2:
## FILE_UPLOAD_MAX_FILESIZE in Bytes gioi han toi da dung luog file co the upload 
FILE_UPLOAD_MAX_FILESIZE=10000000
## so luong file toi da
FILE_UPLOAD_MAX_FILE_COUNT=5
## duoi file dc phep 
FILE_UPLOAD_EXT=png|jpg|pdf|xlsx
## tien to truoc ten file tren server 
FILE_UPLOAD_PREFIX=cak
## folder luu file 
FILE_DIRECTORY_V2=upload-v2
## url ghep lay file domain name
FILE_LINK=http://127.0.0.1:3000

# HTTP_REQUEST
## Thời gian tối đa cho 1 request. tính theo ms
HTTP_RES_REP_GATEWAY_TIMEOUT=30000

# ENDREGION

# Import excel
## Nơi lưu trữ file dữ liệu lỗi quá trình import excel 
## User truy cập đến đường dẫn trực tiếp để tải file
## Ví dụ: https://domain/upload-v2-error/file_name.xlsx
FILE_DIRECTORY_V2_ERROR=upload-v2-error

#schedule

#REGION BUSSINESS_L0GIC_CONFIG - Cấu hình cụ thể cho từng ứng dụng khác nhau:

#REGION PERSISTENT_CONFIG - Cấu hình persistent
## Main database
PG_MAIN_DB_HOST=***********
PG_MAIN_DB_PORT=5432
PG_MAIN_DB_USER=postgres
PG_MAIN_DB_PASSWORD=postgre
PG_MAIN_DB_DATABASE=social_travel_trip

MIGRATIONS_PG_MAIN_DB_HOST=${PG_MAIN_DB_HOST}
MIGRATIONS_PG_MAIN_DB_PORT=${PG_MAIN_DB_PORT}
MIGRATIONS_PG_MAIN_DB_USER=${PG_MAIN_DB_USER}
MIGRATIONS_PG_MAIN_DB_PASSWORD=${PG_MAIN_DB_PASSWORD}
MIGRATIONS_PG_MAIN_DB_DATABASE=${PG_MAIN_DB_DATABASE}

# IO_REDIS_MAIN_MAX_CLIENT=100
# IO_REDIS_MAIN_HOST=localhost
# IO_REDIS_MAIN_PORT=6379
# IO_REDIS_MAIN_DB_NAME=0

# IO_REDIS_QUEUE_MAX_CLIENT=100
# IO_REDIS_QUEUE_HOST=localhost
# IO_REDIS_QUEUE_PORT=6379
# IO_REDIS_QUEUE_DB_NAME=0

## NOTE: ...
# PG_OLD_DB_HOST=xxx
# PG_OLD_DB_PORT=5432
# PG_OLD_DB_USER=xxx
# PG_OLD_DB_PASSWORD=xxx
# PG_OLD_DB_DATABASE=xxx

## NOTE: ... 
SQL_HOST_ANH_QUAN=xxx
SQL_PORT_ANH_QUAN=1433
SQL_USER_ANH_QUAN=xxx
SQL_PASSWORD_ANH_QUAN=xxx
SQL_DATABASE_ANH_QUAN=xxx

## Setup Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Zm9uZC1idWxsLTcxLmNsZXJrLmFjY291bnRzLmRldiQ
CLERK_SECRET_KEY=sk_test_IW9Qe99GsK9nZODkMK9Bl0Yf7xdFvmo5hqATcEV9hG