/* Chat Animation Improvements */

/* Smooth message entrance animation */
@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  50% {
    opacity: 0.7;
    transform: translateY(10px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced fade in animation */
@keyframes enhancedFadeIn {
  0% {
    opacity: 0;
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    filter: blur(0);
  }
}

/* Scroll to bottom button animation */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(20px);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Message bubble hover effect */
.message-bubble {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* New message animation classes */
.new-message-enter {
  animation: slideInFromBottom 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.new-message-fade {
  animation: enhancedFadeIn 0.5s ease-out;
}

/* Scroll button animation */
.scroll-button-enter {
  animation: bounceIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Prevent animation interference during typing */
.typing-mode .message-container {
  scroll-behavior: auto;
}

.typing-mode .new-message-enter {
  animation-duration: 0.3s;
}

/* Smooth scroll area */
.chat-scroll-area {
  scroll-behavior: smooth;
  scroll-padding-bottom: 100px; /* Account for input area */
}

/* Input area improvements */
.chat-input-area {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .chat-input-area {
  background: rgba(17, 24, 39, 0.95);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Ensure animations don't cause layout shift */
.message-container {
  contain: layout style;
}

/* Optimize animation performance */
.animate-optimized {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Remove will-change after animation */
.animate-optimized.animation-complete {
  will-change: auto;
}
