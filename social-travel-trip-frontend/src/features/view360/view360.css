/* View 360 Enhanced Styles */

/* Smooth transitions for all view360 components */
.view360-container * {
  transition: all 0.2s ease-in-out;
}

/* Enhanced loading animations */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.view360-loading {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  background: linear-gradient(-45deg, #8b5cf6, #3b82f6, #06b6d4, #8b5cf6);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced hover effects */
.view360-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.view360-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Smooth backdrop blur transitions */
.backdrop-blur-enhanced {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
}

/* Enhanced button animations */
.view360-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.view360-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.view360-button:hover::before {
  left: 100%;
}

/* Search results animation */
.search-result-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced iframe loading */
.google-maps-iframe-container {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  width: 100%;
  height: 100%;
}

.google-maps-iframe {
  transition: opacity 0.5s ease-in-out;
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  border: none !important;
  border-radius: 0.75rem;
  margin: 0 !important;
  padding: 0 !important;
  vertical-align: top;
  background: transparent;
  position: relative;
  z-index: 1;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .view360-container {
    padding: 1rem;
  }

  .view360-card {
    margin: 0.5rem 0;
  }
}

/* Remove any extra spacing that might cause white space */
.view360-container * {
  box-sizing: border-box;
}

/* Ensure no margin/padding issues */
[data-radix-tabs-content] {
  margin: 0 !important;
  padding: 0 !important;
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .view360-container {
    color-scheme: dark;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .view360-container *,
  .view360-loading,
  .gradient-text,
  .view360-card,
  .view360-button,
  .search-result-item {
    animation: none !important;
    transition: none !important;
  }
}

/* Focus states for accessibility */
.view360-focusable:focus {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
}

/* Enhanced scrollbar for search results */
.view360-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.view360-scrollbar::-webkit-scrollbar-track {
  background: rgba(156, 163, 175, 0.1);
  border-radius: 3px;
}

.view360-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 3px;
}

.view360-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}

/* Search container z-index fix */
.view360-search-container {
  position: relative;
  z-index: 1000;
  isolation: isolate;
}

.view360-search-results {
  position: absolute;
  z-index: 9999 !important;
  top: 100% !important;
  left: 0 !important;
  right: 0 !important;
  margin-top: 0.5rem !important;
}

/* Create new stacking context for search */
.view360-search-section {
  position: relative;
  z-index: 100;
  isolation: isolate;
}
